<?php
defined('BASEPATH') or exit('No direct script access allowed');


class ModuleController extends CI_Controller
{

   public function __construct()
   {
      parent::__construct();
      $this->load->helper(array('form', 'url'));
      $this->load->library('upload');
      $this->load->model('ModuleModel');
      $this->load->library('aws_s3');
   }

   /**
    * Helper function to handle file upload and video transcoding
    * @param string $fileTmpName - Temporary file path
    * @param string $fileName - Original file name
    * @param string $s3Folder - S3 folder name (e.g., 'shortReading', 'shortVideo')
    * @return array - Returns array with file_url and transcoded video URLs
    */
   private function handleFileUploadAndTranscoding($fileTmpName, $fileName, $s3Folder)
   {
      // Log debug information
      log_message('debug', "Starting file upload and transcoding - File: $fileName, Temp: $fileTmpName, Folder: $s3Folder");
      log_message('debug', "Current working directory: " . getcwd());
      log_message('debug', "FCPATH constant: " . FCPATH);
      // Initialize return values
      $result = [
         'file_url' => null,
         'video_url_360p' => null,
         'video_url_720p' => null,
         'video_url_1080p' => null
      ];

      if (empty($fileTmpName)) {
         return $result;
      }

      // Generate a unique key for the S3 object
      $key = $s3Folder . '/' . uniqid() . '_' . $fileName;

      // Upload the original file to AWS S3
      $result['file_url'] = $this->aws_s3->upload($fileTmpName, $key);

      // Get file extension to determine if it's a video file
      $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
      $videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', '3gp'];

      // Only transcode if it's a video file
      if (in_array($fileExtension, $videoExtensions)) {
         // Set transcoding folder path using absolute path
         $transcodingFolder = FCPATH . 'assets/transcoding/';

         // Build output paths for each resolution
         $baseFileName = pathinfo($fileName, PATHINFO_FILENAME);
         $output360p = $transcodingFolder . '360p_' . $baseFileName . '.mp4';
         $output720p = $transcodingFolder . '720p_' . $baseFileName . '.mp4';
         $output1080p = $transcodingFolder . '1080p_' . $baseFileName . '.mp4';

         // Ensure the transcoding folder exists and is writable
         if (!is_dir($transcodingFolder)) {
            if (!mkdir($transcodingFolder, 0777, true)) {
               log_message('error', "Failed to create transcoding directory: $transcodingFolder");
               return $result; // Return early if directory creation fails
            }
         }

         // Verify the directory is writable
         if (!is_writable($transcodingFolder)) {
            log_message('error', "Transcoding directory is not writable: $transcodingFolder");
            return $result; // Return early if directory is not writable
         }

         // Transcode video to different resolutions with error handling
         $this->transcodeAndUpload($fileTmpName, $output360p, $s3Folder, '360p', $result);
         $this->transcodeAndUpload($fileTmpName, $output720p, $s3Folder, '720p', $result);
         $this->transcodeAndUpload($fileTmpName, $output1080p, $s3Folder, '1080p', $result);
      }

      return $result;
   }

   /**
    * Debug method to check upload environment and configuration
    */
   private function debugUploadEnvironment()
   {
      log_message('debug', '=== UPLOAD DEBUG INFO ===');
      log_message('debug', 'PHP Version: ' . phpversion());
      log_message('debug', 'upload_max_filesize: ' . ini_get('upload_max_filesize'));
      log_message('debug', 'post_max_size: ' . ini_get('post_max_size'));
      log_message('debug', 'max_execution_time: ' . ini_get('max_execution_time'));
      log_message('debug', 'memory_limit: ' . ini_get('memory_limit'));
      log_message('debug', 'file_uploads: ' . (ini_get('file_uploads') ? 'enabled' : 'disabled'));
      log_message('debug', 'upload_tmp_dir: ' . ini_get('upload_tmp_dir'));
      log_message('debug', 'Current working directory: ' . getcwd());
      log_message('debug', 'FCPATH: ' . FCPATH);
      log_message('debug', 'Assets directory exists: ' . (is_dir(FCPATH . 'assets') ? 'yes' : 'no'));
      log_message('debug', 'Assets directory writable: ' . (is_writable(FCPATH . 'assets') ? 'yes' : 'no'));
      log_message('debug', 'Transcoding directory exists: ' . (is_dir(FCPATH . 'assets/transcoding') ? 'yes' : 'no'));
      log_message('debug', 'Transcoding directory writable: ' . (is_writable(FCPATH . 'assets/transcoding') ? 'yes' : 'no'));

      // Check $_FILES array
      if (!empty($_FILES)) {
         log_message('debug', 'FILES array received: ' . json_encode(array_keys($_FILES)));
         foreach ($_FILES as $key => $file) {
            if (is_array($file['name'])) {
               log_message('debug', "File array $key structure: " . json_encode($file));
            } else {
               log_message('debug', "File $key: name={$file['name']}, size={$file['size']}, error={$file['error']}");
            }
         }
      } else {
         log_message('debug', 'No FILES received in request');
      }

      // Check POST data size
      log_message('debug', 'POST data size: ' . strlen(serialize($_POST)) . ' bytes');
      log_message('debug', 'Content-Length header: ' . ($_SERVER['CONTENT_LENGTH'] ?? 'not set'));

      // Test AWS S3 connectivity
      try {
         $this->load->library('aws_s3');
         log_message('debug', 'AWS S3 library loaded successfully');
      } catch (Exception $e) {
         log_message('error', 'Failed to load AWS S3 library: ' . $e->getMessage());
      }

      // Check FFmpeg availability
      exec('ffmpeg -version 2>&1', $ffmpeg_output, $ffmpeg_return);
      if ($ffmpeg_return === 0) {
         log_message('debug', 'FFmpeg is available: ' . $ffmpeg_output[0]);
      } else {
         log_message('error', 'FFmpeg is not available or not in PATH');
      }

      log_message('debug', '=== END UPLOAD DEBUG INFO ===');
   }

   /**
    * Helper function to transcode video and upload to S3
    */
   private function transcodeAndUpload($inputFile, $outputFile, $s3Folder, $resolution, &$result)
   {
      $scale = ($resolution === '360p') ? '360' : (($resolution === '720p') ? '720' : '1080');
      $cmd = "ffmpeg -i " . escapeshellarg($inputFile) . " -vf scale=-1:$scale -c:v libx264 -crf 18 " . escapeshellarg($outputFile) . " 2>&1";

      exec($cmd, $output, $return_var);

      // Check if transcoding was successful and file exists
      if ($return_var === 0 && file_exists($outputFile)) {
         $s3Key = $s3Folder . '/' . uniqid() . '_' . $resolution . '.mp4';

         // Verify file exists and is readable before uploading
         if (is_readable($outputFile)) {
            $result['video_url_' . $resolution] = $this->aws_s3->upload($outputFile, $s3Key);
            unlink($outputFile); // Clean up temporary file only after successful upload
         } else {
            log_message('error', "Transcoded file not readable: $outputFile");
         }
      } else {
         log_message('error', "FFmpeg transcoding failed for $resolution. Return code: $return_var. Output: " . implode("\n", $output));
      }
   }



   /**
    * Test endpoint to check basic upload functionality
    */
   public function test_upload()
   {
      $this->debugUploadEnvironment();

      echo "<h2>Upload Test Results</h2>";
      echo "<pre>";
      echo "POST Data:\n";
      print_r($_POST);
      echo "\nFILES Data:\n";
      print_r($_FILES);
      echo "</pre>";

      // Test simple file upload to S3
      if (!empty($_FILES['test_file']['tmp_name'])) {
         $this->load->library('aws_s3');
         $result = $this->aws_s3->upload($_FILES['test_file']['tmp_name'], 'test/' . $_FILES['test_file']['name']);
         echo "<p>S3 Upload Result: " . $result . "</p>";
      }

      // Simple upload form for testing
      echo '<form method="post" enctype="multipart/form-data">';
      echo '<input type="file" name="test_file" required>';
      echo '<input type="submit" value="Test Upload">';
      echo '</form>';
   }

   public function save()
   {
      // Debug information for staging environment
      $this->debugUploadEnvironment();

      // Temporarily show debug info
      echo "<pre>POST Data:\n";
      print_r($_POST);
      echo "\nFILES Data:\n";
      print_r($_FILES);
      echo "</pre>";

      $this->load->helper('url');

      // Retrieve data from form submission
      $courseName = $this->input->post('course_name');
      $description = $this->input->post('description');
      $designation = $this->input->post('businessUnit');
      $is_published = $this->input->post('is_published');

      // Initialize S3 library
      $this->load->library('aws_s3');

      $s3 = $this->aws_s3; // Instantiate S3 library

      // Save Module image if uploaded
      $image_url = '';

      if (!empty($_FILES['image_url']['tmp_name'])) {
         $filePath = $_FILES['image_url']['tmp_name'];
         $fileName = $_FILES['image_url']['name'];

         // Generate a unique key for the S3 object
         $key = 'thumbnail/' . uniqid() . '_' . $fileName;

         // Upload the file to S3
         $image_url = $s3->upload($filePath, $key);
      }

      // Save Certificate image if uploaded
      $certificate = '';

      if (!empty($_FILES['certificate']['tmp_name'])) {
         $filePath = $_FILES['certificate']['tmp_name'];
         $fileName = $_FILES['certificate']['name'];

         // Generate a unique key for the S3 object
         $key = 'certificate/' . uniqid() . '_' . $fileName;

         // Upload the file to S3
         $certificate = $s3->upload($filePath, $key);
      }

      $sections = $this->input->post('sections');

      $businessUnit     = $this->input->post('businessUnit');
      $categorySelect   = $this->input->post('categorySelect');
      $plantillaAssign  = $this->input->post('plantillaAssign');

      $moduleID = $this->ModuleModel->save_course($courseName, $description, $designation, $image_url, $certificate, $is_published);



      foreach ($sections as $sectionIndex => $section) {
         $sectionName = $section['section_name'];
         $difficulty = $section['difficulty'];


         $sectionId = $this->ModuleModel->save_section($moduleID, $sectionName, $difficulty);


         // Loop through each section and short reading content
         if (isset($section['short_reading'])) {
            foreach ($section['short_reading'] as $short_reading => $shortReading) {
               $short_reading_title = $shortReading['short_reading_title'];
               $short_reading_subtitle = $shortReading['short_reading_subtitle'];
               $short_reading_content = $shortReading['short_reading_content'];
               $short_reading_url = $shortReading['short_reading_url'];
               $sequence_num = $shortReading['sequence_num'];

               // Initialize file URLs to null if no file is uploaded
               $uploadResult = [
                  'file_url' => null,
                  'video_url_360p' => null,
                  'video_url_720p' => null,
                  'video_url_1080p' => null
               ];

               // Check if a file is uploaded and the file name is not empty
               if (
                  isset($_FILES['sections']['name'][$sectionIndex]['short_reading'][$short_reading]['short_reading_file']) &&
                  !empty($_FILES['sections']['name'][$sectionIndex]['short_reading'][$short_reading]['short_reading_file'])
               ) {
                  // Prepare file for upload (Extract relevant file details)
                  $fileName = $_FILES['sections']['name'][$sectionIndex]['short_reading'][$short_reading]['short_reading_file'];
                  $fileTmpName = $_FILES['sections']['tmp_name'][$sectionIndex]['short_reading'][$short_reading]['short_reading_file'];

                  // Use helper function to handle upload and transcoding
                  $uploadResult = $this->handleFileUploadAndTranscoding($fileTmpName, $fileName, 'shortReading');
               }

               // Extract values for backward compatibility
               $short_reading_file_url = $uploadResult['file_url'];
               $video_url_360p = $uploadResult['video_url_360p'];
               $video_url_720p = $uploadResult['video_url_720p'];
               $video_url_1080p = $uploadResult['video_url_1080p'];

               // Save the short reading data, including the S3 URLs for videos if they exist
               $this->ModuleModel->save_short_reading(
                  $short_reading_title,
                  $short_reading_subtitle,
                  $short_reading_content,
                  $short_reading_url,
                  $sectionId,
                  $sequence_num,
                  $short_reading_file_url,  // File URL for images/documents
                  $video_url_360p,  // This will be null if no video transcoding was done
                  $video_url_720p,  // This will be null if no video transcoding was done
                  $video_url_1080p  // This will be null if no video transcoding was done
               );
            }
         }




         // Save Short Video
         if (isset($section['short_video'])) {
            foreach ($section['short_video'] as $short_video => $shortVideo) {
               $short_video_title = $shortVideo['short_video_title'];
               $short_video_subtitle = $shortVideo['short_video_subtitle'];
               $short_video_content = $shortVideo['short_video_content'];
               $short_video_url = $shortVideo['short_video_url'];
               $sequence_num = $shortVideo['sequence_num'];

               // Initialize video URLs to null if no file is uploaded
               $short_video_file_url = null;
               $video_url_360p = $video_url_720p = $video_url_1080p = null;

               // Check if a file is uploaded and the file name is not empty
               if (
                  isset($_FILES['sections']['name'][$sectionIndex]['short_video'][$short_video]['short_video_file']) &&
                  !empty($_FILES['sections']['name'][$sectionIndex]['short_video'][$short_video]['short_video_file'])
               ) {

                  // Prepare file for upload (Extract relevant file details)
                  $fileName = $_FILES['sections']['name'][$sectionIndex]['short_video'][$short_video]['short_video_file'];
                  $fileTmpName = $_FILES['sections']['tmp_name'][$sectionIndex]['short_video'][$short_video]['short_video_file'];

                  // Check if temp name exists (file uploaded properly)
                  if (!empty($fileTmpName)) {
                     // Generate a unique key for the S3 object within the shortVideo folder
                     $key = 'shortVideo/' . uniqid() . '_' . $fileName;
                     // Upload the file to AWS S3 and get the file URL
                     $short_video_file_url = $this->aws_s3->upload($fileTmpName, $key); // Upload file to S3

                     // Get file extension to determine if it's a video file
                     $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
                     $videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', '3gp'];

                     // Only transcode if it's a video file
                     if (in_array($fileExtension, $videoExtensions)) {
                        // Set transcoding folder path using absolute path
                        $transcodingFolder = FCPATH . 'assets/transcoding/';

                        // Build output paths for each resolution
                        $output360p = $transcodingFolder . '360p_' . pathinfo($fileName, PATHINFO_FILENAME) . '.mp4';
                        $output720p = $transcodingFolder . '720p_' . pathinfo($fileName, PATHINFO_FILENAME) . '.mp4';
                        $output1080p = $transcodingFolder . '1080p_' . pathinfo($fileName, PATHINFO_FILENAME) . '.mp4';

                        // Ensure the transcoding folder exists before running the transcoding commands
                        if (!is_dir($transcodingFolder)) {
                           mkdir($transcodingFolder, 0777, true); // Create the folder if it doesn't exist
                        }

                        // Transcode video to different resolutions with error handling
                        exec("ffmpeg -i " . escapeshellarg($fileTmpName) . " -vf scale=-1:360 -c:v libx264 -crf 18 " . escapeshellarg($output360p) . " 2>&1", $output, $return_var);
                        if ($return_var === 0 && file_exists($output360p)) {
                           $video_url_360p = $this->aws_s3->upload($output360p, 'shortVideo/' . uniqid() . '_360p.mp4');
                           unlink($output360p); // Clean up temporary file
                        }

                        exec("ffmpeg -i " . escapeshellarg($fileTmpName) . " -vf scale=-1:720 -c:v libx264 -crf 18 " . escapeshellarg($output720p) . " 2>&1", $output, $return_var);
                        if ($return_var === 0 && file_exists($output720p)) {
                           $video_url_720p = $this->aws_s3->upload($output720p, 'shortVideo/' . uniqid() . '_720p.mp4');
                           unlink($output720p); // Clean up temporary file
                        }

                        exec("ffmpeg -i " . escapeshellarg($fileTmpName) . " -vf scale=-1:1080 -c:v libx264 -crf 18 " . escapeshellarg($output1080p) . " 2>&1", $output, $return_var);
                        if ($return_var === 0 && file_exists($output1080p)) {
                           $video_url_1080p = $this->aws_s3->upload($output1080p, 'shortVideo/' . uniqid() . '_1080p.mp4');
                           unlink($output1080p); // Clean up temporary file
                        }
                     }
                     // For non-video files, we just upload the original file
                  }
               }

               // Save the short video data, including the S3 URLs for the video and transcoded versions if they exist
               $this->ModuleModel->save_short_video(
                  $short_video_title,
                  $short_video_subtitle,
                  $short_video_content,
                  $short_video_url,
                  $sectionId,
                  $sequence_num,
                  $short_video_file_url,  // This will be null if no file was uploaded
                  $video_url_360p ?? null,  // This will be null if no transcoding was done
                  $video_url_720p ?? null,  // This will be null if no transcoding was done
                  $video_url_1080p ?? null   // This will be null if no transcoding was done
               );
            }
         }



         // Save Lecture Cast
         if (isset($section['lecture_cast'])) {
            foreach ($section['lecture_cast'] as $lecture_cast_index => $lectureCast) {
               $lecture_cast_title = $lectureCast['lecture_cast_title'];
               $lecture_cast_subtitle = $lectureCast['lecture_cast_subtitle'];
               $lecture_cast_content = $lectureCast['lecture_cast_content'];
               $lecture_cast_url = $lectureCast['lecture_cast_url'];
               $sequence_num = $lectureCast['sequence_num'];

               // Initialize file URLs to null in case no file is uploaded
               $lecture_cast_file_url = null;
               $video_url_360p = $video_url_720p = $video_url_1080p = null;

               // Check if a file is uploaded and the file name is not empty
               if (
                  isset($_FILES['sections']['name'][$sectionIndex]['lecture_cast'][$lecture_cast_index]['lecture_cast_file']) &&
                  !empty($_FILES['sections']['name'][$sectionIndex]['lecture_cast'][$lecture_cast_index]['lecture_cast_file'])
               ) {

                  // Prepare file for upload (Extract relevant file details)
                  $fileName = $_FILES['sections']['name'][$sectionIndex]['lecture_cast'][$lecture_cast_index]['lecture_cast_file'];
                  $fileTmpName = $_FILES['sections']['tmp_name'][$sectionIndex]['lecture_cast'][$lecture_cast_index]['lecture_cast_file'];

                  // Check if temp name exists (file uploaded properly)
                  if (!empty($fileTmpName)) {
                     // Generate a unique key for the S3 object within the lectureCast folder
                     $key = 'lectureCast/' . uniqid() . '_' . $fileName;
                     // Upload the file to AWS S3 and get the file URL
                     $lecture_cast_file_url = $this->aws_s3->upload($fileTmpName, $key); // Upload file to S3

                     // Get file extension to determine if it's a video file
                     $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
                     $videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', '3gp'];

                     // Only transcode if it's a video file
                     if (in_array($fileExtension, $videoExtensions)) {
                        // Set transcoding folder path using absolute path
                        $transcodingFolder = FCPATH . 'assets/transcoding/';

                        // Build output paths for each resolution
                        $output360p = $transcodingFolder . '360p_' . pathinfo($fileName, PATHINFO_FILENAME) . '.mp4';
                        $output720p = $transcodingFolder . '720p_' . pathinfo($fileName, PATHINFO_FILENAME) . '.mp4';
                        $output1080p = $transcodingFolder . '1080p_' . pathinfo($fileName, PATHINFO_FILENAME) . '.mp4';

                        // Ensure the transcoding folder exists before running the transcoding commands
                        if (!is_dir($transcodingFolder)) {
                           mkdir($transcodingFolder, 0777, true); // Create the folder if it doesn't exist
                        }

                        // Transcode video to different resolutions with error handling
                        exec("ffmpeg -i " . escapeshellarg($fileTmpName) . " -vf scale=-1:360 -c:v libx264 -crf 18 " . escapeshellarg($output360p) . " 2>&1", $output, $return_var);
                        if ($return_var === 0 && file_exists($output360p)) {
                           $video_url_360p = $this->aws_s3->upload($output360p, 'lectureCast/' . uniqid() . '_360p.mp4');
                           unlink($output360p); // Clean up temporary file
                        }

                        exec("ffmpeg -i " . escapeshellarg($fileTmpName) . " -vf scale=-1:720 -c:v libx264 -crf 18 " . escapeshellarg($output720p) . " 2>&1", $output, $return_var);
                        if ($return_var === 0 && file_exists($output720p)) {
                           $video_url_720p = $this->aws_s3->upload($output720p, 'lectureCast/' . uniqid() . '_720p.mp4');
                           unlink($output720p); // Clean up temporary file
                        }

                        exec("ffmpeg -i " . escapeshellarg($fileTmpName) . " -vf scale=-1:1080 -c:v libx264 -crf 18 " . escapeshellarg($output1080p) . " 2>&1", $output, $return_var);
                        if ($return_var === 0 && file_exists($output1080p)) {
                           $video_url_1080p = $this->aws_s3->upload($output1080p, 'lectureCast/' . uniqid() . '_1080p.mp4');
                           unlink($output1080p); // Clean up temporary file
                        }
                     }
                     // For non-video files, we just upload the original file
                  }
               }

               // Save the lecture cast data, including the S3 URLs for the video and transcoded versions if they exist
               $this->ModuleModel->save_lecture_cast(
                  $lecture_cast_title,
                  $lecture_cast_subtitle,
                  $lecture_cast_content,
                  $lecture_cast_url,
                  $sectionId,
                  $sequence_num,
                  $lecture_cast_file_url,  // This will be null if no file was uploaded
                  $video_url_360p ?? null,  // This will be null if no transcoding was done
                  $video_url_720p ?? null,  // This will be null if no transcoding was done
                  $video_url_1080p ?? null   // This will be null if no transcoding was done
               );
            }
         }


         // Save Podcast
         if (isset($section['podcast'])) {
            foreach ($section['podcast'] as $podcast_index => $podcast) {
               $podcast_title = $podcast['podcast_title'];
               $podcast_subtitle = $podcast['podcast_subtitle'];
               $podcast_content = $podcast['podcast_content'];
               $podcast_url = $podcast['podcast_url'];
               $sequence_num = $podcast['sequence_num'];

               // Initialize file URLs to null in case no file is uploaded
               $podcast_file_url = null;
               $video_url_360p = $video_url_720p = $video_url_1080p = null;

               // Check if a file is uploaded and the file name is not empty
               if (
                  isset($_FILES['sections']['name'][$sectionIndex]['podcast'][$podcast_index]['podcast_file']) &&
                  !empty($_FILES['sections']['name'][$sectionIndex]['podcast'][$podcast_index]['podcast_file'])
               ) {

                  // Prepare file for upload (Extract relevant file details)
                  $fileName = $_FILES['sections']['name'][$sectionIndex]['podcast'][$podcast_index]['podcast_file'];
                  $fileTmpName = $_FILES['sections']['tmp_name'][$sectionIndex]['podcast'][$podcast_index]['podcast_file'];

                  // Check if temp name exists (file uploaded properly)
                  if (!empty($fileTmpName)) {
                     // Generate a unique key for the S3 object within the podcast folder
                     $key = 'podcast/' . uniqid() . '_' . $fileName;
                     // Upload the file to AWS S3 and get the file URL
                     $podcast_file_url = $this->aws_s3->upload($fileTmpName, $key); // Upload file to S3

                     // Get file extension to determine if it's a video file
                     $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
                     $videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', '3gp'];

                     // Only transcode if it's a video file
                     if (in_array($fileExtension, $videoExtensions)) {
                        // Set transcoding folder path using absolute path
                        $transcodingFolder = FCPATH . 'assets/transcoding/';

                        // Build output paths for each resolution
                        $output360p = $transcodingFolder . '360p_' . pathinfo($fileName, PATHINFO_FILENAME) . '.mp4';
                        $output720p = $transcodingFolder . '720p_' . pathinfo($fileName, PATHINFO_FILENAME) . '.mp4';
                        $output1080p = $transcodingFolder . '1080p_' . pathinfo($fileName, PATHINFO_FILENAME) . '.mp4';

                        // Ensure the transcoding folder exists before running the transcoding commands
                        if (!is_dir($transcodingFolder)) {
                           mkdir($transcodingFolder, 0777, true); // Create the folder if it doesn't exist
                        }

                        // Transcode video to different resolutions with error handling
                        exec("ffmpeg -i " . escapeshellarg($fileTmpName) . " -vf scale=-1:360 -c:v libx264 -crf 18 " . escapeshellarg($output360p) . " 2>&1", $output, $return_var);
                        if ($return_var === 0 && file_exists($output360p)) {
                           $video_url_360p = $this->aws_s3->upload($output360p, 'podcast/' . uniqid() . '_360p.mp4');
                           unlink($output360p); // Clean up temporary file
                        }

                        exec("ffmpeg -i " . escapeshellarg($fileTmpName) . " -vf scale=-1:720 -c:v libx264 -crf 18 " . escapeshellarg($output720p) . " 2>&1", $output, $return_var);
                        if ($return_var === 0 && file_exists($output720p)) {
                           $video_url_720p = $this->aws_s3->upload($output720p, 'podcast/' . uniqid() . '_720p.mp4');
                           unlink($output720p); // Clean up temporary file
                        }

                        exec("ffmpeg -i " . escapeshellarg($fileTmpName) . " -vf scale=-1:1080 -c:v libx264 -crf 18 " . escapeshellarg($output1080p) . " 2>&1", $output, $return_var);
                        if ($return_var === 0 && file_exists($output1080p)) {
                           $video_url_1080p = $this->aws_s3->upload($output1080p, 'podcast/' . uniqid() . '_1080p.mp4');
                           unlink($output1080p); // Clean up temporary file
                        }
                     }
                     // For non-video files (audio files, images, etc.), we just upload the original file
                  }
               }

               // Save the podcast data, including the S3 URLs for the podcast file and transcoded videos if they exist
               $this->ModuleModel->save_podcast(
                  $podcast_title,
                  $podcast_subtitle,
                  $podcast_content,
                  $podcast_url,
                  $sectionId,
                  $sequence_num,
                  $podcast_file_url,  // This will be null if no file was uploaded
                  $video_url_360p ?? null,  // This will be null if no transcoding was done
                  $video_url_720p ?? null,  // This will be null if no transcoding was done
                  $video_url_1080p ?? null   // This will be null if no transcoding was done
               );
            }
         }



         // Save Reflective Writing
         if (isset($section['reflective_writing'])) {
            foreach ($section['reflective_writing'] as $reflective_index => $reflective) {
               $reflective_writing_title = $reflective['reflective_writing_title'];
               $reflective_writing_question = $reflective['reflective_writing_question'];
               $reflective_writing_limit = $reflective['reflective_writing_limit'];
               $sequence_num = $reflective['sequence_num'];

               // Initialize file URL to null
               $reflective_file_url = null;

               // Check if a file is uploaded with correct input name 'reflective_writing_image'
               if (
                  isset($_FILES['sections']['name'][$sectionIndex]['reflective_writing'][$reflective_index]['reflective_writing_image']) &&
                  !empty($_FILES['sections']['name'][$sectionIndex]['reflective_writing'][$reflective_index]['reflective_writing_image'])
               ) {

                  $fileName = $_FILES['sections']['name'][$sectionIndex]['reflective_writing'][$reflective_index]['reflective_writing_image'];
                  $fileTmpName = $_FILES['sections']['tmp_name'][$sectionIndex]['reflective_writing'][$reflective_index]['reflective_writing_image'];

                  if (!empty($fileTmpName)) {
                     $key = 'reflective_writing/' . uniqid() . '_' . $fileName;
                     $reflective_file_url = $this->aws_s3->upload($fileTmpName, $key); // Upload to S3
                  }
               }

               // Save reflective writing data using the model (with file URL)
               if (!$this->ModuleModel->save_reflective_writing(
                  $reflective_writing_title,
                  $reflective_writing_question,
                  $reflective_writing_limit,
                  $sectionId,
                  $sequence_num,
                  $reflective_file_url // optional image
               )) {
                  log_message('error', 'Failed to save reflective writing data.');
                  echo json_encode(['error' => 'Failed to save reflective writing data.']);
                  return;
               }
            }

            echo json_encode(['success' => 'Reflective writing data saved successfully.']);
         }
         // No error message needed - reflective writing is optional









         // Save Short Assessment
         if (isset($section['short_assessment'])) {
            foreach ($section['short_assessment'] as $assessmentIndex => $assessment) {
               $short_assessment_question = $assessment['short_assessment_question'];
               $sequence_num = $assessment['sequence_num'];

               // Prepare choices data
               $choices = [];
               $correctAnswer = null; // Initialize correctAnswer to null

               foreach ($assessment as $key => $value) {
                  if (preg_match('/^choice_(\d+)$/', $key, $matches)) {
                     $choiceNumber = $matches[1];
                     $choices[$choiceNumber] = $value;

                     // Check if this choice is the correct answer
                     if ($assessment['correct_answer'] === "choice_$choiceNumber") {
                        $correctAnswer = $choiceNumber; // Set correctAnswer to the actual correct answer choice number
                     }
                  }
               }

               // Save short assessment data using the model
               if (!$this->ModuleModel->save_short_assessment(
                  $short_assessment_question,
                  $choices,
                  $correctAnswer,
                  $sectionId,
                  $sequence_num
               )) {
                  // Handle errors
                  log_message('error', 'Failed to save short assessment data.');
                  echo json_encode(['error' => 'Failed to save short assessment data.']);
                  return;
               }
            }

            // Return success response
            echo json_encode(['success' => 'Short assessment data saved successfully.']);
         }
         // No error message needed - short assessment is optional



         // Save Multiple Choice Sets
         if (isset($section['multiple_choice_set'])) {
            foreach ($section['multiple_choice_set'] as $setIndex => $set) {
               $set_title = $set['set_title'];
               $sequence_num = $set['sequence_num'];

               // Save Multiple Choice Set and retrieve set_id
               $set_id = $this->ModuleModel->save_multiple_choice_set(
                  $set_title,
                  $sectionId,
                  $sequence_num
               );

               if (!$set_id) {
                  log_message('error', 'Failed to save multiple choice set.');
                  echo json_encode(['error' => 'Failed to save multiple choice set.']);
                  return;
               }

               // Save Questions
               if (isset($set['questions'])) {
                  foreach ($set['questions'] as $questionIndex => $question) {
                     $question_text = $question['question_text'];
                     $correct_ans_num = $question['correct_ans_num'] ?? null;

                     // Save question
                     $question_id = $this->ModuleModel->save_multiple_choice_question(
                        $set_id,
                        $question_text,
                        $correct_ans_num
                     );

                     if (!$question_id) {
                        log_message('error', 'Failed to save multiple choice question.');
                        echo json_encode(['error' => 'Failed to save multiple choice question.']);
                        return;
                     }

                     // Save Choices
                     if (isset($question['choices'])) {
                        foreach ($question['choices'] as $choiceIndex => $choice) {
                           $choice_text = $choice['choice_text'];

                           if (!$this->ModuleModel->save_multiple_choice_choice(
                              $question_id,
                              $choiceIndex,
                              $choice_text
                           )) {
                              log_message('error', 'Failed to save multiple choice choice.');
                              echo json_encode(['error' => 'Failed to save multiple choice choice.']);
                              return;
                           }
                        }
                     }
                  }
               }
            }

            echo json_encode(['success' => 'Multiple choice data saved successfully.']);
         }
         // No error message needed - multiple choice is optional
      }

   $this->ModuleModel->save_category($moduleID, $businessUnit, $categorySelect, $plantillaAssign);
   }

   public function success()
   {
      echo "Course saved successfully!";
   }
}
